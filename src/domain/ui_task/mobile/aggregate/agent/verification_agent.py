#!/usr/bin/env python3
"""
预期结果验证Agent

负责验证步骤执行结果是否符合预期，当决策agent推进步骤时触发验证
"""

import json
import time
from datetime import datetime
from typing import Dict, Any, Optional

from langchain_core.prompts import ChatPromptTemplate
from loguru import logger

from src.domain.ui_task.mobile.aggregate.prompt.verification_prompt import get_step_verification_prompt, \
    get_user_step_verification_invoke_prompt
from src.domain.ui_task.mobile.android.image_processor import ImageProcessor
from src.domain.ui_task.mobile.android.screenshot_manager import convert_screenshot_to_base64, take_screenshot
from src.domain.ui_task.mobile.repo.do.State import DeploymentState
from src.infra.model import get_chat_model


class ExpectationVerificationAgent:
    """预期结果验证Agent"""

    def __init__(self):
        self.model = get_chat_model()

    @staticmethod
    def _escape_template_variables(text: str) -> str:
        """
        转义文本中的模板变量符号，防止LangChain将其识别为变量

        Args:
            text: 原始文本

        Returns:
            转义后的文本
        """
        if not text:
            return text

        # 将单个花括号转义为双花括号
        # 这样LangChain就不会将其识别为模板变量
        return text.replace("{", "{{").replace("}", "}}")

    def verify_step_expectation(
            self,
            state: DeploymentState,
            previous_step_index: int,
            expected_text: Optional[str] = None,
            expected_image: Optional[str] = None,
            wait_time: float = 2.5,
            max_retries: int = 2
    ) -> Dict[str, Any]:
        """
        验证上一步骤是否符合预期结果

        Args:
            state: 当前状态
            previous_step_index: 上一步骤索引
            expected_text: 期望的文字描述
            expected_image: 期望的图片（base64）
            wait_time: 验证前等待时间（秒），默认2.5秒
            max_retries: 最大重试次数，默认2次（第一次等待指定时间，第二次立即重试）

        Returns:
            验证结果字典
        """
        try:
            logger.info(f"[{state['task_id']}] 🔍 Verifying step {previous_step_index + 1} expectation...")

            # 获取步骤信息
            task_steps = state.get("task_steps", [])
            if previous_step_index >= len(task_steps):
                return {
                    "verified": False,
                    "reason": "Step index out of range",
                    "timestamp": datetime.now().isoformat()
                }

            previous_step_name = task_steps[previous_step_index]

            # 检查是否已经验证过这个步骤，只有成功的验证结果才缓存，失败的允许重新验证
            step_verification_results = state.get("step_verification_results", [])
            if (previous_step_index < len(step_verification_results) and
                    step_verification_results[previous_step_index] and
                    step_verification_results[previous_step_index].get("verified") is not None):
                existing_result = step_verification_results[previous_step_index]
                # 只有验证成功的结果才直接返回，失败的结果允许重新验证
                if existing_result.get("verified"):
                    logger.info(
                        f"[{state['task_id']}] ℹ️ Step {previous_step_index + 1} already verified successfully, skipping re-verification")
                    return existing_result
                else:
                    logger.info(
                        f"[{state['task_id']}] ℹ️ Step {previous_step_index + 1} previously failed verification, allowing re-verification")
                    # 清除失败的验证结果，允许重新验证
                    step_verification_results[previous_step_index] = {}

            # 处理期望图片：将文件路径转换为base64
            processed_expected_image = None
            if expected_image:
                processed_expected_image = ImageProcessor.convert_image_path_to_base64(expected_image)
                if processed_expected_image is None:
                    logger.warning(
                        f"[{state['task_id']}] ⚠️ Failed to convert expected image to base64: {expected_image}")
                else:
                    logger.info(f"[{state['task_id']}] ✅ Successfully converted expected image to base64")
            else:
                logger.info(f"[{state['task_id']}] ℹ️ No expected image provided for verification")

            # 重试验证逻辑
            for attempt in range(max_retries):
                try:
                    # 第一次尝试等待用户指定时间，后续重试立即执行
                    if attempt == 0:
                        logger.info(f"[{state['task_id']}] 📸 First verification attempt, waiting {wait_time} seconds before screenshot...")
                        time.sleep(wait_time)
                    else:
                        logger.info(f"[{state['task_id']}] 🔄 Retry attempt {attempt + 1}, taking immediate screenshot...")

                    # 截图
                    screenshot_path = take_screenshot(
                        device=state["device"],
                        task_id=state["task_id"],
                        action_name=f"verify_step_expectation_attempt_{attempt + 1}"
                    )

                    current_screenshot_base64 = convert_screenshot_to_base64(screenshot_path, state["task_id"])

                    # 构建验证提示
                    verification_prompt = get_step_verification_prompt(
                        previous_step_name=previous_step_name,
                        expected_text=expected_text,
                        expected_image=processed_expected_image,
                    )
                    print(verification_prompt)
                    messages = [{
                        "role": "system",
                        "content": verification_prompt
                    },
                        {
                            "role": "user",
                            "content": [
                                {
                                    "type": "text",
                                    "text": "########## 当前界面 ##########"
                                },
                                {
                                    "type": "image_url",
                                    "image_url": {
                                        "url": f"data:image/png;base64,{current_screenshot_base64}"
                                    }
                                }
                            ]
                        }
                    ]

                    if processed_expected_image:
                        messages.append(
                            {
                                "role": "user",
                                "content": [
                                    {
                                        "type": "text",
                                        "text": "########## 期望图片 ##########"
                                    },
                                    {
                                        "type": "image_url",
                                        "image_url": {
                                            "url": f"data:image/png;base64,{processed_expected_image}"
                                        }
                                    }
                                ]
                            }
                        )

                    messages.append({
                        "role": "user",
                        "content": get_user_step_verification_invoke_prompt()
                    })

                    # 调用模型
                    start_time = time.time()
                    prompt = ChatPromptTemplate.from_messages(messages=messages)
                    chain = prompt | self.model
                    output = chain.invoke({})
                    model_response = output.content
                    end_time = time.time()

                    logger.info(f"[{state['task_id']}] 验证耗时: {end_time - start_time:.2f}s")
                    logger.info(f"[{state['task_id']}] 验证响应 (attempt {attempt + 1}): {model_response}")

                    # 解析JSON结果
                    parsed_result = self._parse_verification_result(model_response, state)

                    verification_result = {
                        "verified": parsed_result["verified"],
                        "reason": parsed_result["reason"],
                        "timestamp": datetime.now().isoformat(),
                        "attempt": attempt + 1
                    }

                    # 如果验证成功，直接返回结果
                    if parsed_result["verified"]:
                        logger.info(f"[{state['task_id']}] ✅ Step {previous_step_index + 1} verification successful on attempt {attempt + 1}")

                        # 更新验证结果到状态中
                        step_verification_results = state.get("step_verification_results", [])
                        while len(step_verification_results) <= previous_step_index:
                            step_verification_results.append({})
                        step_verification_results[previous_step_index] = verification_result
                        state["step_verification_results"] = step_verification_results

                        # 立即记录监督日志
                        try:
                            from src.domain.ui_task.mobile.service.task_persistence_service import task_persistence_service
                            from src.domain.ui_task.mobile.service.execution_log_service import ExecutionLogService

                            verified = verification_result.get("verified", False)
                            reason = verification_result.get("reason", "")
                            result_text = "通过" if verified else "未通过"

                            supervisor_log = ExecutionLogService.create_supervisor_log(result_text, reason)
                            task_persistence_service.append_execution_log_entries(state["task_id"], [supervisor_log])
                            logger.info(f"[{state['task_id']}] 👁️ Supervisor logged immediately")
                        except Exception as e:
                            logger.warning(f"[{state['task_id']}] Failed to log supervisor: {str(e)}")

                        return verification_result
                    else:
                        logger.warning(f"[{state['task_id']}] ❌ Step {previous_step_index + 1} verification failed on attempt {attempt + 1}: {parsed_result['reason']}")

                        # 如果是最后一次尝试，保存失败结果并返回
                        if attempt == max_retries - 1:
                            logger.error(f"[{state['task_id']}] ❌ Step {previous_step_index + 1} verification failed after {max_retries} attempts")

                            # 更新验证结果到状态中
                            step_verification_results = state.get("step_verification_results", [])
                            while len(step_verification_results) <= previous_step_index:
                                step_verification_results.append({})
                            step_verification_results[previous_step_index] = verification_result
                            state["step_verification_results"] = step_verification_results

                            # 立即记录监督日志（失败情况）
                            try:
                                from src.domain.ui_task.mobile.service.task_persistence_service import task_persistence_service
                                from src.domain.ui_task.mobile.service.execution_log_service import ExecutionLogService

                                verified = verification_result.get("verified", False)
                                reason = verification_result.get("reason", "")
                                result_text = "通过" if verified else "未通过"

                                supervisor_log = ExecutionLogService.create_supervisor_log(result_text, reason)
                                task_persistence_service.append_execution_log_entries(state["task_id"], [supervisor_log])
                                logger.info(f"[{state['task_id']}] 👁️ Supervisor logged immediately (failed)")
                            except Exception as e:
                                logger.warning(f"[{state['task_id']}] Failed to log supervisor: {str(e)}")

                            return verification_result

                except Exception as attempt_error:
                    logger.error(f"[{state['task_id']}] ❌ Error in verification attempt {attempt + 1}: {str(attempt_error)}")

                    # 如果是最后一次尝试，返回错误结果
                    if attempt == max_retries - 1:
                        return {
                            "verified": False,
                            "reason": f"Verification error after {max_retries} attempts: {str(attempt_error)}",
                            "timestamp": datetime.now().isoformat(),
                            "attempt": attempt + 1
                        }

        except Exception as e:
            logger.error(f"[{state['task_id']}] ❌ Error in expectation verification: {str(e)}")
            return {
                "verified": False,
                "reason": f"Verification error: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }

    def _parse_verification_result(self, model_response: str, state: DeploymentState) -> Dict[str, Any]:
        """
        解析验证结果JSON

        Args:
            model_response: 模型响应文本
            state: 当前状态

        Returns:
            包含verified和reason的字典
        """
        try:
            import re

            # 提取JSON部分
            json_match = re.search(r'\{.*}', model_response, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                result = json.loads(json_str)

                # 返回verified和reason
                if "verified" in result:
                    return {
                        "verified": result["verified"],
                        "reason": result.get("reason", "")
                    }

            # 如果没有找到JSON，默认返回失败
            return {
                "verified": False,
                "reason": "无法解析验证响应"
            }

        except Exception as e:
            logger.error(f"[{state['task_id']}] ❌ 验证结果JSON解析失败: {str(e)}")
            return {
                "verified": False,
                "reason": f"JSON解析错误: {str(e)}"
            }


expectation_verification_agent = ExpectationVerificationAgent()
